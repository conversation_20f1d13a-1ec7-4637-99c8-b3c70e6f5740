@echo off
echo ========================================
echo Express Budget Tracker Setup
echo ========================================
echo.

echo Installing dependencies...
call npm install

echo.
echo Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Created .env file from template
) else (
    echo .env file already exists
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file with your MongoDB URI
echo 2. Run: npm run initdb (to create sample data)
echo 3. Run: npm run devstart (to start the server)
echo.
echo Then visit: http://localhost:3000
echo Login with: demo / password123
echo.
pause
